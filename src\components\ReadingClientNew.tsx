'use client';

import { useState } from 'react';
import { useLanguage } from '@/contexts/LanguageContext';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import AuthModal from '@/components/AuthModal';
import AdSense from '@/components/AdSense';
import LoadingButton from '@/components/LoadingButton';

interface ReadingPassage {
  id: string;
  title: string;
  level: 'beginner' | 'intermediate' | 'advanced';
  topic: string;
  readingTime: string;
  wordCount: number;
  passage: string;
  vocabulary: Array<{
    word: string;
    definition: string;
    context: string;
    pronunciation?: string;
    partOfSpeech?: string;
  }>;
  comprehensionQuestions: Array<{
    id: string;
    type: 'multiple-choice' | 'true-false';
    question: string;
    options: string[];
    correctAnswer: number;
    explanation: string;
    difficulty: string;
  }>;
  discussionQuestions?: string[];
  keyThemes?: string[];
  culturalNotes?: string;
}

export default function ReadingClient() {
  const { t } = useLanguage();
  const [showAuthModal, setShowAuthModal] = useState(false);
  const [selectedPassage, setSelectedPassage] = useState<ReadingPassage | null>(null);
  const [selectedLevel, setSelectedLevel] = useState<string>('beginner');
  const [selectedTopic, setSelectedTopic] = useState<string>('');
  const [selectedType, setSelectedType] = useState<string>('general');
  const [wordCount, setWordCount] = useState<number>(300);
  const [isGenerating, setIsGenerating] = useState(false);
  const [showVocabulary, setShowVocabulary] = useState(false);
  const [userAnswers, setUserAnswers] = useState<Record<string, number>>({});
  const [showResults, setShowResults] = useState(false);

  const passageTypes = [
    { id: 'general', name: 'General', description: 'General informative content' },
    { id: 'news', name: 'News', description: 'News article style' },
    { id: 'story', name: 'Story', description: 'Narrative stories' },
    { id: 'academic', name: 'Academic', description: 'Academic text style' },
    { id: 'science', name: 'Science', description: 'Science topics' },
    { id: 'history', name: 'History', description: 'Historical content' },
    { id: 'culture', name: 'Culture', description: 'Cultural topics' }
  ];

  const topicSuggestions = [
    'Technology and Innovation',
    'Environmental Protection', 
    'Health and Wellness',
    'Travel and Culture',
    'Education and Learning',
    'Food and Nutrition',
    'Sports and Recreation',
    'Art and Literature',
    'Business and Economics',
    'Science and Discovery'
  ];

  const levels = ['beginner', 'intermediate', 'advanced'];

  const generateReadingPassage = async () => {
    if (isGenerating) return;
    
    setIsGenerating(true);
    try {
      const response = await fetch('/api/ai/reading', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          level: selectedLevel,
          language: 'en',
          topic: selectedTopic || undefined,
          wordCount,
          passageType: selectedType
        }),
      });

      const data = await response.json();
      
      if (data.success) {
        const passage = {
          id: `ai_${Date.now()}`,
          ...data.passage
        };
        setSelectedPassage(passage);
        setUserAnswers({});
        setShowResults(false);
        setShowVocabulary(false);
      } else {
        throw new Error(data.error || 'Failed to generate passage');
      }
    } catch (error) {
      console.error('Error generating reading passage:', error);
      alert(t('failedToGeneratePassage'));
    } finally {
      setIsGenerating(false);
    }
  };

  const getLevelColor = (level: string) => {
    switch (level) {
      case 'beginner': return 'text-green-600 bg-green-100';
      case 'intermediate': return 'text-orange-600 bg-orange-100';
      case 'advanced': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const handleAnswerSelect = (questionId: string, answerIndex: number) => {
    setUserAnswers(prev => ({
      ...prev,
      [questionId]: answerIndex
    }));
  };

  const calculateScore = () => {
    if (!selectedPassage) return { correct: 0, total: 0 };
    
    let correct = 0;
    selectedPassage.comprehensionQuestions.forEach(question => {
      if (userAnswers[question.id] === question.correctAnswer) {
        correct++;
      }
    });
    
    return { correct, total: selectedPassage.comprehensionQuestions.length };
  };

  if (selectedPassage) {
    const score = calculateScore();
    
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50">
        <Header
          onAuthClick={() => setShowAuthModal(true)}
          showBackButton={true}
          onBackClick={() => {
            setSelectedPassage(null);
            setUserAnswers({});
            setShowResults(false);
            setShowVocabulary(false);
          }}
        />

        <div className="pt-24 pb-8 px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl mx-auto">
            {/* Passage Header */}
            <div className="bg-white/80 backdrop-blur-xl rounded-2xl p-6 mb-6 shadow-xl border border-white/20">
              <div className="flex items-center justify-between mb-4">
                <div>
                  <h1 className="text-3xl font-bold text-gray-800">{selectedPassage.title}</h1>
                  <div className="flex items-center space-x-4 mt-2 text-sm text-gray-600">
                    <span>📖 {selectedPassage.readingTime}</span>
                    <span>📝 {selectedPassage.wordCount} words</span>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getLevelColor(selectedPassage.level)}`}>
                      {selectedPassage.level}
                    </span>
                    <span className="px-2 py-1 bg-gray-100 rounded-full text-xs">
                      {selectedPassage.topic}
                    </span>
                  </div>
                </div>
                <div className="flex space-x-2">
                  <button
                    onClick={() => setShowVocabulary(!showVocabulary)}
                    className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                      showVocabulary 
                        ? 'bg-blue-500 text-white' 
                        : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                    }`}
                  >
                    📚 Vocabulary
                  </button>
                </div>
              </div>

              {showVocabulary && (
                <div className="mb-4 p-4 bg-blue-50 rounded-lg">
                  <h3 className="font-semibold text-blue-800 mb-3">Key Vocabulary:</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                    {selectedPassage.vocabulary.map((item, index) => (
                      <div key={index} className="bg-white p-3 rounded-lg">
                        <div className="font-medium text-blue-700 flex items-center space-x-2">
                          <span>{item.word}</span>
                          {item.pronunciation && (
                            <span className="text-xs text-gray-500">/{item.pronunciation}/</span>
                          )}
                          {item.partOfSpeech && (
                            <span className="text-xs text-blue-500 bg-blue-100 px-2 py-1 rounded">
                              {item.partOfSpeech}
                            </span>
                          )}
                        </div>
                        <div className="text-sm text-gray-600 mb-1">{item.definition}</div>
                        <div className="text-xs text-gray-500 italic">"{item.context}"</div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>

            {/* Reading Passage */}
            <div className="bg-white/80 backdrop-blur-xl rounded-2xl p-8 mb-6 shadow-xl border border-white/20">
              <div className="prose max-w-none text-gray-800 leading-relaxed">
                {selectedPassage.passage.split('\n\n').map((paragraph, index) => (
                  <p key={index} className="mb-4">
                    {paragraph}
                  </p>
                ))}
              </div>
            </div>

            {/* Comprehension Questions */}
            <div className="bg-white/80 backdrop-blur-xl rounded-2xl p-6 shadow-xl border border-white/20">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-xl font-semibold text-gray-800">Comprehension Questions</h3>
                {showResults && (
                  <div className="text-lg font-semibold text-blue-600">
                    Score: {score.correct}/{score.total} ({Math.round((score.correct / score.total) * 100)}%)
                  </div>
                )}
              </div>

              <div className="space-y-6">
                {selectedPassage.comprehensionQuestions.map((question, index) => (
                  <div key={question.id} className="p-4 bg-gray-50 rounded-lg">
                    <div className="flex items-center justify-between mb-3">
                      <h4 className="font-semibold text-gray-800">
                        {index + 1}. {question.question}
                      </h4>
                      <span className={`px-2 py-1 rounded-full text-xs ${
                        question.difficulty === 'easy' ? 'bg-green-100 text-green-600' :
                        question.difficulty === 'medium' ? 'bg-orange-100 text-orange-600' :
                        'bg-red-100 text-red-600'
                      }`}>
                        {question.difficulty}
                      </span>
                    </div>
                    
                    <div className="space-y-2 mb-4">
                      {question.options.map((option, optionIndex) => {
                        const isSelected = userAnswers[question.id] === optionIndex;
                        const isCorrect = optionIndex === question.correctAnswer;
                        const showCorrect = showResults && isCorrect;
                        const showIncorrect = showResults && isSelected && !isCorrect;
                        
                        return (
                          <button
                            key={optionIndex}
                            onClick={() => !showResults && handleAnswerSelect(question.id, optionIndex)}
                            disabled={showResults}
                            className={`w-full text-left p-3 rounded-lg border transition-colors ${
                              showCorrect
                                ? 'bg-green-100 border-green-500 text-green-700'
                                : showIncorrect
                                ? 'bg-red-100 border-red-500 text-red-700'
                                : isSelected
                                ? 'bg-blue-100 border-blue-500 text-blue-700'
                                : 'bg-white border-gray-300 hover:bg-gray-100'
                            } ${showResults ? 'cursor-not-allowed' : 'cursor-pointer'}`}
                          >
                            {option}
                          </button>
                        );
                      })}
                    </div>

                    {showResults && (
                      <div className="p-3 bg-blue-50 text-blue-700 rounded-lg">
                        <p className="font-medium">
                          ✅ Correct answer: {question.options[question.correctAnswer]}
                        </p>
                        <p className="text-sm mt-1">{question.explanation}</p>
                      </div>
                    )}
                  </div>
                ))}
              </div>

              <div className="mt-6 text-center">
                {!showResults ? (
                  <button
                    onClick={() => setShowResults(true)}
                    disabled={Object.keys(userAnswers).length !== selectedPassage.comprehensionQuestions.length}
                    className="px-6 py-3 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-lg hover:from-blue-600 hover:to-purple-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                  >
                    {t('submitAnswers')}
                  </button>
                ) : (
                  <div className="space-x-4">
                    <button
                      onClick={() => {
                        setUserAnswers({});
                        setShowResults(false);
                      }}
                      className="px-6 py-3 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors"
                    >
                      {t('tryAgain')}
                    </button>
                    <button
                      onClick={() => setSelectedPassage(null)}
                      className="px-6 py-3 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors"
                    >
                      {t('generateNew')}
                    </button>
                  </div>
                )}
              </div>

              {/* Additional Content */}
              {selectedPassage.keyThemes && selectedPassage.keyThemes.length > 0 && (
                <div className="mt-6 p-4 bg-purple-50 rounded-lg">
                  <h4 className="font-semibold text-purple-800 mb-2">Key Themes:</h4>
                  <div className="flex flex-wrap gap-2">
                    {selectedPassage.keyThemes.map((theme, index) => (
                      <span key={index} className="px-3 py-1 bg-purple-100 text-purple-700 rounded-full text-sm">
                        {theme}
                      </span>
                    ))}
                  </div>
                </div>
              )}

              {selectedPassage.culturalNotes && (
                <div className="mt-4 p-4 bg-yellow-50 rounded-lg">
                  <h4 className="font-semibold text-yellow-800 mb-2">Cultural Notes:</h4>
                  <p className="text-yellow-700 text-sm">{selectedPassage.culturalNotes}</p>
                </div>
              )}
            </div>
          </div>

          {/* AdSense Banner */}
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 mt-8">
            <AdSense
              adSlot="3456789012"
              adFormat="auto"
              className="rounded-lg"
              style={{ display: 'block', minHeight: '100px' }}
              lazy={true}
            />
          </div>
        </div>

        <AuthModal
          isOpen={showAuthModal}
          onClose={() => setShowAuthModal(false)}
        />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50">
      <Header
        onAuthClick={() => setShowAuthModal(true)}
        showBackButton={true}
        onBackClick={() => window.history.back()}
      />

      <div className="pt-24 pb-8 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          {/* Educational Content Header */}
          <div className="mb-8 bg-white/80 backdrop-blur-xl rounded-2xl p-8 shadow-xl border border-white/20">
            <div className="text-center mb-6">
              <h1 className="text-4xl sm:text-5xl font-bold bg-gradient-to-r from-gray-800 via-blue-800 to-purple-800 bg-clip-text text-transparent mb-4">
                {t('readingTitle')}
              </h1>
              <p className="text-lg text-gray-600 max-w-3xl mx-auto mb-6">
                {t('readingSubtitle')}
              </p>
            </div>

            {/* Reading Benefits */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
              <div className="text-center p-4 bg-blue-50 rounded-lg">
                <div className="text-3xl mb-2">📖</div>
                <h3 className="font-semibold text-blue-800 mb-1">Comprehension</h3>
                <p className="text-blue-700 text-sm">Improve reading comprehension skills</p>
              </div>
              <div className="text-center p-4 bg-green-50 rounded-lg">
                <div className="text-3xl mb-2">📚</div>
                <h3 className="font-semibold text-green-800 mb-1">Vocabulary</h3>
                <p className="text-green-700 text-sm">Expand vocabulary through context</p>
              </div>
              <div className="text-center p-4 bg-purple-50 rounded-lg">
                <div className="text-3xl mb-2">🧠</div>
                <h3 className="font-semibold text-purple-800 mb-1">Critical Thinking</h3>
                <p className="text-purple-700 text-sm">Develop analytical skills</p>
              </div>
              <div className="text-center p-4 bg-orange-50 rounded-lg">
                <div className="text-3xl mb-2">🌍</div>
                <h3 className="font-semibold text-orange-800 mb-1">Cultural Awareness</h3>
                <p className="text-orange-700 text-sm">Learn about different cultures</p>
              </div>
            </div>

            {/* Reading Strategies */}
            <div className="bg-gradient-to-r from-blue-50 to-purple-50 p-6 rounded-lg mb-6">
              <h3 className="text-lg font-semibold text-gray-800 mb-3">Effective Reading Strategies</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                <div>
                  <h4 className="font-medium text-blue-800 mb-2">🔍 Preview First</h4>
                  <p className="text-gray-700">Scan the title, headings, and first sentences to get an overview.</p>
                </div>
                <div>
                  <h4 className="font-medium text-green-800 mb-2">📝 Active Reading</h4>
                  <p className="text-gray-700">Take notes and ask questions while reading to stay engaged.</p>
                </div>
                <div>
                  <h4 className="font-medium text-purple-800 mb-2">🔄 Review & Reflect</h4>
                  <p className="text-gray-700">Summarize key points and reflect on what you've learned.</p>
                </div>
              </div>
            </div>

            {/* Reading Skills Development */}
            <div className="bg-gray-50 p-6 rounded-lg">
              <h3 className="text-lg font-semibold text-gray-800 mb-3">Skills You'll Develop</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div className="space-y-2">
                  <div className="flex items-center">
                    <span className="w-2 h-2 bg-blue-500 rounded-full mr-2"></span>
                    <span>Skimming and scanning techniques</span>
                  </div>
                  <div className="flex items-center">
                    <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                    <span>Inference and deduction skills</span>
                  </div>
                  <div className="flex items-center">
                    <span className="w-2 h-2 bg-purple-500 rounded-full mr-2"></span>
                    <span>Context clue recognition</span>
                  </div>
                </div>
                <div className="space-y-2">
                  <div className="flex items-center">
                    <span className="w-2 h-2 bg-orange-500 rounded-full mr-2"></span>
                    <span>Main idea identification</span>
                  </div>
                  <div className="flex items-center">
                    <span className="w-2 h-2 bg-red-500 rounded-full mr-2"></span>
                    <span>Supporting detail analysis</span>
                  </div>
                  <div className="flex items-center">
                    <span className="w-2 h-2 bg-indigo-500 rounded-full mr-2"></span>
                    <span>Author's purpose understanding</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Header Section */}
          <div className="text-center mb-12">
            <h2 className="text-2xl font-bold text-gray-800 mb-4">
              Generate Your Reading Passage
            </h2>
            <p className="text-gray-600">
              Create a personalized reading passage with comprehension questions
            </p>
          </div>

          {/* Generation Controls */}
          <div className="bg-white/80 backdrop-blur-xl rounded-2xl p-8 mb-8 shadow-xl border border-white/20">
            <h3 className="text-xl font-semibold text-gray-800 mb-6">{t('generateReading')}</h3>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
              {/* Level Selection */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">{t('level')}</label>
                <select
                  value={selectedLevel}
                  onChange={(e) => setSelectedLevel(e.target.value)}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  {levels.map(level => (
                    <option key={level} value={level}>
                      {level.charAt(0).toUpperCase() + level.slice(1)}
                    </option>
                  ))}
                </select>
              </div>

              {/* Type Selection */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">{t('style')}</label>
                <select
                  value={selectedType}
                  onChange={(e) => setSelectedType(e.target.value)}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  {passageTypes.map(type => (
                    <option key={type.id} value={type.id}>{type.name}</option>
                  ))}
                </select>
              </div>

              {/* Word Count */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">{t('wordCount')}</label>
                <select
                  value={wordCount}
                  onChange={(e) => setWordCount(parseInt(e.target.value))}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value={200}>200 words</option>
                  <option value={300}>300 words</option>
                  <option value={400}>400 words</option>
                  <option value={500}>500 words</option>
                </select>
              </div>

              {/* Generate Button */}
              <div className="flex items-end">
                <LoadingButton
                  onClick={generateReadingPassage}
                  isLoading={isGenerating}
                  loadingText={t('generating')}
                  className="w-full px-6 py-2 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-lg hover:from-blue-600 hover:to-purple-700 transition-colors font-medium"
                >
                  ✨ {t('generatePassage')}
                </LoadingButton>
              </div>
            </div>

            {/* Topic Input */}
            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Topic (Optional - leave blank for AI to choose)
              </label>
              <input
                type="text"
                value={selectedTopic}
                onChange={(e) => setSelectedTopic(e.target.value)}
                placeholder="e.g., Technology, Environment, Health..."
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>

            {/* Topic Suggestions */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Or choose from popular topics:
              </label>
              <div className="flex flex-wrap gap-2">
                {topicSuggestions.map((topic, index) => (
                  <button
                    key={index}
                    onClick={() => setSelectedTopic(topic)}
                    className="px-3 py-1 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-full text-sm transition-colors"
                  >
                    {topic}
                  </button>
                ))}
              </div>
            </div>
          </div>

          {/* Instructions */}
          <div className="bg-white/80 backdrop-blur-xl rounded-2xl p-6 shadow-xl border border-white/20">
            <h3 className="text-lg font-semibold text-gray-800 mb-4">How it works:</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="text-center">
                <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
                  <span className="text-blue-600 font-bold">1</span>
                </div>
                <h4 className="font-medium text-gray-800 mb-2">Choose Settings</h4>
                <p className="text-sm text-gray-600">Select your level, style, and topic preferences</p>
              </div>

              <div className="text-center">
                <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-3">
                  <span className="text-purple-600 font-bold">2</span>
                </div>
                <h4 className="font-medium text-gray-800 mb-2">{t('aiGenerates')}</h4>
                <p className="text-sm text-gray-600">{t('aiGeneratesDesc')}</p>
              </div>

              <div className="text-center">
                <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3">
                  <span className="text-green-600 font-bold">3</span>
                </div>
                <h4 className="font-medium text-gray-800 mb-2">Practice & Learn</h4>
                <p className="text-sm text-gray-600">Read, answer questions, and improve your skills</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <AuthModal
        isOpen={showAuthModal}
        onClose={() => setShowAuthModal(false)}
      />

      <Footer />
    </div>
  );
}
